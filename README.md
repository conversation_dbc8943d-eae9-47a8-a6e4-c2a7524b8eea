# Netflix Profile Icons Scraper

这是一个用于爬取Netflix头像图片的Python脚本，可以从Imgur画廊页面自动下载所有Netflix头像图标。

## 功能特点

- 🚀 自动处理动态加载的内容
- 📜 智能滚动加载更多图片
- 🖼️ 自动下载所有发现的头像图片
- 📁 自动创建下载目录
- 🔄 避免重复下载
- 📝 详细的日志记录

## 安装依赖

首先确保你已经安装了Python 3.7+，然后安装所需的依赖包：

```bash
pip install -r requirements.txt
```

## 使用方法

### 基本使用

直接运行脚本：

```bash
python netflix_profile_scraper.py
```

### 自定义参数

你也可以在代码中修改以下参数：

- `download_dir`: 下载目录（默认：`netflix_icons`）
- `max_scrolls`: 最大滚动次数（默认：15）
- `target_url`: 目标页面URL

## 工作原理

1. **初始化浏览器**: 使用Selenium WebDriver启动Chrome浏览器
2. **访问页面**: 打开目标Imgur画廊页面
3. **智能滚动**: 自动滚动页面以加载更多内容
4. **图片识别**: 查找所有`img.image-placeholder`元素
5. **下载图片**: 下载发现的新图片到本地目录
6. **去重处理**: 避免重复下载相同的图片

## 输出

- 所有下载的图片将保存在`netflix_icons`目录中
- 控制台会显示详细的下载进度和统计信息
- 每张图片都会以原始文件名保存

## 注意事项

- 首次运行时会自动下载Chrome驱动程序
- 确保网络连接稳定
- 某些图片可能因为网络问题下载失败，脚本会继续处理其他图片
- 如果遇到反爬虫机制，可以适当增加延迟时间

## 故障排除

### Chrome驱动问题
如果遇到Chrome驱动相关问题，脚本会自动下载匹配的驱动版本。

### 网络连接问题
如果下载失败，检查网络连接并重新运行脚本。

### 页面加载问题
如果页面加载缓慢，可以增加等待时间或减少滚动次数。

## 许可证

本项目仅供学习和研究使用，请遵守相关网站的使用条款。
