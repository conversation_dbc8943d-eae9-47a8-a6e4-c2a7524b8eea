#!/usr/bin/env python3
"""
Netflix头像链接提取器 - 只提取链接，不下载
"""

import time
import re
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service

def convert_to_original_link(imgur_link):
    """将Imgur链接转换为原图链接"""
    # 移除_d.webp等后缀，获取原始图片
    # 例如: https://i.imgur.com/zBr1CQ3_d.webp?maxwidth=760&fidelity=grand
    # 转换为: https://i.imgur.com/zBr1CQ3.png
    
    # 提取图片ID
    match = re.search(r'imgur\.com/([a-zA-Z0-9]+)', imgur_link)
    if match:
        img_id = match.group(1)
        return f"https://i.imgur.com/{img_id}.png"
    return imgur_link

def save_links_to_file(links, filename="netflix_profile_links.txt"):
    """保存链接到文件"""
    with open(filename, 'w', encoding='utf-8') as f:
        f.write("Netflix Profile Icons Links\n")
        f.write("=" * 50 + "\n\n")
        for i, link in enumerate(links, 1):
            # 转换为高质量原图链接
            original_link = convert_to_original_link(link)
            f.write(f"{i}. {original_link}\n")
    print(f"✅ 链接已保存到: {filename}")

def extract_netflix_icon_links():
    """提取Netflix头像链接"""
    # 设置Chrome选项
    options = Options()
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")
    options.add_argument("--headless")  # 无头模式，更快
    
    # 初始化浏览器
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=options)
    
    try:
        url = "https://imgur.com/gallery/netflix-all-profile-icons-ToZ21Gg"
        print(f"🌐 正在访问: {url}")
        driver.get(url)
        
        # 等待页面加载
        time.sleep(5)
        
        all_links = set()
        
        # 滚动并收集图片链接
        for scroll in range(10):
            print(f"📜 第 {scroll + 1} 次滚动...")
            
            # 查找图片
            images = driver.find_elements(By.CSS_SELECTOR, "img.image-placeholder")
            print(f"🖼️  发现 {len(images)} 张图片")
            
            # 收集新链接
            new_links = 0
            for img in images:
                src = img.get_attribute("src")
                if src and src not in all_links:
                    all_links.add(src)
                    new_links += 1
                    print(f"🔗 新链接: {convert_to_original_link(src)}")
            
            print(f"📊 本次新增 {new_links} 个链接，总计 {len(all_links)} 个")
            
            # 如果没有新链接，可能已经到底了
            if new_links == 0:
                print("🏁 没有发现新链接，可能已经加载完毕")
                break
            
            # 滚动页面
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(3)
        
        # 保存链接到文件
        if all_links:
            save_links_to_file(list(all_links))
            print(f"\n🎉 提取完成！总共找到 {len(all_links)} 个Netflix头像链接")
            
            # 显示前几个链接作为示例
            print("\n📋 前5个链接预览:")
            for i, link in enumerate(list(all_links)[:5], 1):
                print(f"  {i}. {convert_to_original_link(link)}")
        else:
            print("❌ 没有找到任何图片链接")
        
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    extract_netflix_icon_links()
