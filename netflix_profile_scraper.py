#!/usr/bin/env python3
"""
Netflix Profile Icons Scraper

This script scrapes Netflix profile icons from the Imgur gallery page.
It uses Selenium to handle dynamic content loading and scrolling.
"""

import os
import time
import requests
from urllib.parse import urlparse
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NetflixProfileScraper:
    def __init__(self, download_dir="netflix_icons"):
        """
        初始化爬虫
        
        Args:
            download_dir (str): 下载图片的目录
        """
        self.download_dir = download_dir
        self.driver = None
        self.downloaded_urls = set()
        
        # 创建下载目录
        if not os.path.exists(self.download_dir):
            os.makedirs(self.download_dir)
            logger.info(f"创建下载目录: {self.download_dir}")
    
    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        # 如果不想看到浏览器界面，可以取消注释下面这行
        # chrome_options.add_argument("--headless")
        
        try:
            # 自动下载并设置Chrome驱动
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            logger.info("Chrome浏览器驱动初始化成功")
        except Exception as e:
            logger.error(f"Chrome浏览器驱动初始化失败: {e}")
            raise
    
    def download_image(self, img_url, filename):
        """
        下载图片
        
        Args:
            img_url (str): 图片URL
            filename (str): 保存的文件名
        """
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            response = requests.get(img_url, headers=headers, timeout=30)
            response.raise_for_status()
            
            filepath = os.path.join(self.download_dir, filename)
            with open(filepath, 'wb') as f:
                f.write(response.content)
            
            logger.info(f"下载成功: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"下载失败 {img_url}: {e}")
            return False
    
    def scroll_and_load_images(self, url, max_scrolls=10):
        """
        滚动页面并加载图片
        
        Args:
            url (str): 目标页面URL
            max_scrolls (int): 最大滚动次数
        """
        try:
            logger.info(f"正在访问页面: {url}")
            self.driver.get(url)
            
            # 等待页面加载
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "img"))
            )
            
            scroll_count = 0
            last_image_count = 0
            
            while scroll_count < max_scrolls:
                # 查找所有图片元素
                images = self.driver.find_elements(By.CSS_SELECTOR, "img.image-placeholder")
                current_image_count = len(images)
                
                logger.info(f"第 {scroll_count + 1} 次滚动，发现 {current_image_count} 张图片")
                
                # 处理当前页面的图片
                for img in images:
                    try:
                        img_src = img.get_attribute("src")
                        if img_src and img_src not in self.downloaded_urls:
                            # 提取文件名
                            parsed_url = urlparse(img_src)
                            filename = os.path.basename(parsed_url.path)
                            if not filename or '.' not in filename:
                                filename = f"netflix_icon_{len(self.downloaded_urls) + 1}.png"
                            
                            # 下载图片
                            if self.download_image(img_src, filename):
                                self.downloaded_urls.add(img_src)
                    
                    except Exception as e:
                        logger.error(f"处理图片时出错: {e}")
                        continue
                
                # 如果图片数量没有增加，说明可能已经加载完毕
                if current_image_count == last_image_count:
                    logger.info("图片数量未增加，可能已加载完毕")
                    break
                
                last_image_count = current_image_count
                
                # 滚动到页面底部
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                
                # 等待新内容加载
                time.sleep(3)
                scroll_count += 1
            
            logger.info(f"滚动完成，总共下载了 {len(self.downloaded_urls)} 张图片")
            
        except TimeoutException:
            logger.error("页面加载超时")
        except Exception as e:
            logger.error(f"滚动加载过程中出错: {e}")
    
    def scrape(self, url="https://imgur.com/gallery/netflix-all-profile-icons-ToZ21Gg", max_scrolls=10):
        """
        开始爬取
        
        Args:
            url (str): 目标页面URL
            max_scrolls (int): 最大滚动次数
        """
        try:
            self.setup_driver()
            self.scroll_and_load_images(url, max_scrolls)
            
        except Exception as e:
            logger.error(f"爬取过程中出错: {e}")
        
        finally:
            if self.driver:
                self.driver.quit()
                logger.info("浏览器已关闭")
    
    def __del__(self):
        """析构函数，确保浏览器被关闭"""
        if hasattr(self, 'driver') and self.driver:
            self.driver.quit()

def main():
    """主函数"""
    scraper = NetflixProfileScraper()
    
    # 开始爬取
    target_url = "https://imgur.com/gallery/netflix-all-profile-icons-ToZ21Gg"
    scraper.scrape(target_url, max_scrolls=15)
    
    print(f"\n爬取完成！图片已保存到 '{scraper.download_dir}' 目录")
    print(f"总共下载了 {len(scraper.downloaded_urls)} 张图片")

if __name__ == "__main__":
    main()
