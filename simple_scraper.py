#!/usr/bin/env python3
"""
简化版Netflix头像爬虫
"""

import os
import time
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service

def download_image(url, filename, folder="netflix_icons"):
    """下载图片"""
    if not os.path.exists(folder):
        os.makedirs(folder)
    
    try:
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        filepath = os.path.join(folder, filename)
        with open(filepath, 'wb') as f:
            f.write(response.content)
        print(f"✅ 下载成功: {filename}")
        return True
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return False

def scrape_netflix_icons():
    """爬取Netflix头像"""
    # 设置Chrome选项
    options = Options()
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")
    
    # 初始化浏览器
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=options)
    
    try:
        url = "https://imgur.com/gallery/netflix-all-profile-icons-ToZ21Gg"
        print(f"🌐 正在访问: {url}")
        driver.get(url)
        
        # 等待页面加载
        time.sleep(5)
        
        downloaded_urls = set()
        
        # 滚动并收集图片
        for scroll in range(10):
            print(f"📜 第 {scroll + 1} 次滚动...")
            
            # 查找图片
            images = driver.find_elements(By.CSS_SELECTOR, "img.image-placeholder")
            print(f"🖼️  发现 {len(images)} 张图片")
            
            # 下载新图片
            for i, img in enumerate(images):
                src = img.get_attribute("src")
                if src and src not in downloaded_urls:
                    filename = f"netflix_icon_{len(downloaded_urls) + 1}.png"
                    if download_image(src, filename):
                        downloaded_urls.add(src)
            
            # 滚动页面
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(3)
        
        print(f"\n🎉 爬取完成！总共下载了 {len(downloaded_urls)} 张图片")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    scrape_netflix_icons()
